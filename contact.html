<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Contact Stress Free Events and Weddings for wedding planning services in the DFW area.">
    <meta name="keywords" content="contact wedding planner, wedding consultation, DFW wedding planner contact">
    <title>Contact Us - Stress Free Events and Weddings</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <style>
        /* Additional styles specific to contact page */

        /* FAQ Section Styles */
        .faq-section {
            background-color: var(--secondary-color);
            padding: 100px 0;
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            margin-bottom: 20px;
            background-color: var(--white);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }

        .faq-question {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .faq-question::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            transition: var(--transition);
        }

        .faq-item.active .faq-question::after {
            transform: rotate(180deg);
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .faq-item.active .faq-answer {
            padding: 0 20px 20px;
            max-height: 1000px;
        }
        .contact-hero {
            height: 50vh;
            min-height: 350px;
            background-image: url('https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1600&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            margin-top: 90px;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
        }

        .contact-hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            padding: 0 20px;
        }

        .contact-section {
            padding: 100px 0;
            background-color: var(--white);
        }

        .contact-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
        }

        .contact-info {
            padding-right: 30px;
        }

        .contact-info h3 {
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        .contact-detail {
            display: flex;
            align-items: flex-start;
            margin-bottom: 30px;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 20px;
            flex-shrink: 0;
        }

        .contact-text h4 {
            margin-bottom: 5px;
            font-size: 1.2rem;
        }

        .contact-text p {
            margin-bottom: 0;
            color: var(--text-light);
        }

        .contact-form-container {
            background-color: var(--light-bg);
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .contact-form-container h3 {
            margin-bottom: 30px;
            font-size: 1.8rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-radius: var(--border-radius);
            font-family: 'Montserrat', sans-serif;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(216, 180, 226, 0.2);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .map-section {
            padding: 0;
            height: 450px;
            position: relative;
        }

        .map-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .map-container iframe {
            width: 100%;
            height: 100%;
            border: 0;
        }

        @media (max-width: 992px) {
            .contact-container {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .contact-info {
                padding-right: 0;
            }
        }

        @media (max-width: 576px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .contact-form-container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <header class="site-header">
        <div class="container">
            <div class="logo-container">
                <a href="index.html"><img src="./Current Docs/Assets/newlogo.png" alt="Stress Free Logo" class="logo"></a>
            </div>
            <nav class="main-nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="services.html">Services</a></li>
                    <!-- <li><a href="gallery.html">Gallery</a></li>
                    <li><a href="blog.html">Blog</a></li> -->
                    <li><a href="contact.html" class="active">Contact</a></li>
                </ul>
            </nav>
            <div class="header-cta">
                <a href="contact.html" class="btn btn-primary">Book Consultation</a>
            </div>
            <button class="mobile-menu-toggle">
                <span class="hamburger"></span>
            </button>
        </div>
    </header>

    <main>
        <!-- Contact Hero Section -->
        <section class="contact-hero">
            <div class="contact-hero-content" data-aos="fade-up">
                <h1>Contact Us</h1>
                <p class="lead">Let's start planning your dream wedding</p>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact-section">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2>Get In Touch</h2>
                    <div class="divider"><span></span></div>
                    <p>We'd love to hear from you! Reach out to schedule a consultation or ask any questions about our services.</p>
                </div>
                <div class="contact-container">
                    <div class="contact-info" data-aos="fade-right">
                        <h3>Contact Information</h3>
                        <div class="contact-detail">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Our Location</h4>
                                <p>2550 El Dorado Pkwy</p>
                                <p>Mckinney, TX 75070</p>
                                <br>
                                <h4>Service Area</h4>
                                <p>Dallas-Fort Worth Metroplex</p>
                                <p>Texas, United States</p>
                            </div>
                        </div>
                        <div class="contact-detail">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Phone Number</h4>
                                <p>(*************</p>
                            </div>
                        </div>
                        <div class="contact-detail">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Email Address</h4>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-detail">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Office Hours</h4>
                                <p>Monday - Friday: 10:00 AM - 6:00 PM</p>
                                <p>Saturday: 10:00 AM - 3:00 PM</p>
                                <p>Sunday: Closed</p>
                            </div>
                        </div>
                    </div>
                    <div class="btn-block" data-aos="fade-up" data-aos-delay="100">
                        <a href="https://513445.17hats.com/p#/scheduling/zhbhdhxrvhxvptzhndsdnwxckzbnhwnv" class="btn btn-primary btn-block">Book Your Consultation</a>
                        <br>
                        <img src="https://images.unsplash.com/photo-1519741347686-c1e0aadf4611?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" alt="Wedding couple" class="rounded-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Map Section -->
        <section class="map-section">
            <div class="map-container">
                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d429153.0450607487!2d-97.10899484179685!3d32.82054459868428!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x864c19f77b45974b%3A0xb9ec9ba4f647678f!2sDallas-Fort%20Worth%20Metropolitan%20Area%2C%20TX!5e0!3m2!1sen!2sus!4v1650123456789!5m2!1sen!2sus" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq-section">
            <div class="container">
                <div class="section-header" data-aos="fade-up">
                    <h2>Frequently Asked Questions</h2>
                    <div class="divider"><span></span></div>
                </div>
                <div class="faq-container">
                    <div class="faq-item" data-aos="fade-up" data-aos-delay="100">
                        <div class="faq-question">What happens after I submit my contact form?</div>
                        <div class="faq-answer">
                            <p>After submitting your contact form, you can expect to hear back from us within 24-48 hours. We'll reach out to schedule an initial consultation, which can be conducted in person, over the phone, or via video call, depending on your preference.</p>
                        </div>
                    </div>
                    <div class="faq-item" data-aos="fade-up" data-aos-delay="150">
                        <div class="faq-question">Is there a fee for the initial consultation?</div>
                        <div class="faq-answer">
                            <p>No, we offer complimentary initial consultations. This gives us a chance to get to know each other, discuss your vision, and determine if we're a good fit for your wedding planning needs.</p>
                        </div>
                    </div>
                    <div class="faq-item" data-aos="fade-up" data-aos-delay="200">
                        <div class="faq-question">Do you travel for weddings outside the DFW area?</div>
                        <div class="faq-answer">
                            <p>Yes, we do! While we're based in the Dallas-Fort Worth area, we're happy to travel for destination weddings or events in other locations. Additional travel fees may apply depending on the location.</p>
                        </div>
                    </div>
                    <div class="faq-item" data-aos="fade-up" data-aos-delay="250">
                        <div class="faq-question">How soon should I contact you before my wedding date?</div>
                        <div class="faq-answer">
                            <p>We recommend reaching out as soon as possible after your engagement, especially if you're interested in our full-service planning package. For month-of coordination, contacting us 6-8 months before your wedding date is ideal to ensure availability.</p>
                        </div>
                    </div>
                    <div class="faq-item" data-aos="fade-up" data-aos-delay="300">
                        <div class="faq-question">What if I need to reschedule my consultation?</div>
                        <div class="faq-answer">
                            <p>We understand that schedules can change. If you need to reschedule your consultation, simply contact us at least 24 hours in advance, and we'll be happy to find a new time that works for you.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="./Current Docs/Assets/newlogowhite.png" alt="Stress Free Logo" class="logo">
                    <p>Making your special day truly stress-free</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="services.html">Services</a></li>
                        <!-- <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="blog.html">Blog</a></li> -->
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-map-marker-alt"></i> Dallas-Fort Worth Area</p>
                    <p><i class="fas fa-phone"></i> (*************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-social">
                    <h3>Follow Us</h3>
                    <div class="social-icons">
                        <a href="https://www.facebook.com/stressfreeeventsandweddings" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="https://www.instagram.com/stressfreeeventsandweddings" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="https://www.pinterest.com/stressfreee0570/" aria-label="Pinterest"><i class="fab fa-pinterest-p"></i></a>
                        <a href="https://www.tiktok.com/@stressfreeeventsweddings" aria-label="Tiktok"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Stress-Free Events and Weddings. All Rights Reserved.</p>
                <p>Site by: <a href="https://www.media.bytesbyblinken.com">BytesByBlinken Media</a></p>
            </div>
        </div>
    </footer>

    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="script.js"></script>
    <script>
        // Contact form submission
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic validation
            let isValid = true;
            const requiredFields = this.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            });

            // Email validation
            const emailField = this.querySelector('input[type="email"]');
            if (emailField && emailField.value) {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(emailField.value)) {
                    isValid = false;
                    emailField.classList.add('error');
                }
            }

            if (isValid) {
                // In a real implementation, you would send the form data to a server
                alert('Thank you for your message! We will get back to you within 24-48 hours.');
                this.reset();
            } else {
                alert('Please fill in all required fields correctly.');
            }
        });

        // FAQ Accordion
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const faqItem = question.parentElement;
                faqItem.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
